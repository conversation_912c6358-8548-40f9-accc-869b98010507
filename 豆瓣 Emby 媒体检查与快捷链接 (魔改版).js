// ==UserScript==
// @name         豆瓣 Emby 媒体检查与快捷链接 (豪华美化版)
// @namespace    http://tampermonkey.net/
// @version      3.3
// @description  在豆瓣电影页面检查您的 Emby 服务器中是否存在该媒体，并提供 PT站、TMDB 和 Emby 的快捷搜索链接。全新现代化UI设计，高级淡雅配色方案。优化匹配逻辑和搜索链接。
// <AUTHOR> & Optimized (豪华美化版)
// @match        https://movie.douban.com/subject/*
// @grant        GM_xmlhttpRequest
// @grant        GM_addStyle
// @connect      embyHost
// @key          提醒：使用此脚本前，请务必修改下方代码中的 embyHost 和 embyApiKey 为您自己的 Emby 服务器地址和API密钥。
// @key          同时，您可能需要根据您的 embyHost 修改上面一行 "@connect embyHost" 中的 "embyHost" 为您的实际主机名 (例如 @connect my.emby.server.com 或 @connect *************)，以便油猴插件授权网络请求。
// ==/UserScript==

(function () {
    'use strict';

    // ==================== 配置区域 ====================

    // --- 用户配置 ---
    // 请务必将此处 "填写1" 修改为您的 Emby 服务器地址，例如 "http://localhost:8096" 或 "https://your.emby.server"
    const embyHost = "";
    // 请务必将此处 "填写2" 修改为您的 Emby API Key
    const embyApiKey = "";

    // --- 站点配置 ---
    const SITES_CONFIG = {
        mteam: { name: '馒头', emoji: '🥟', url: 'https://kp.m-team.cc/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        audiences: { name: '观众', emoji: '👥', url: 'https://audiences.me/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        hhanclub: { name: '憨憨', emoji: '😊', url: 'https://hhanclub.top/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        hdsky: { name: '天空', emoji: '☁️', url: 'https://hdsky.me/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        chdbits: { name: '彩虹岛', emoji: '🌈', url: 'https://chdbits.co/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        ttg: { name: '听听歌', emoji: '🎵', url: 'https://totheglory.im/browse.php?c=M&search_field={title}', searchType: 'title' },
        ourbits: { name: '我堡', emoji: '🏰', url: 'https://ourbits.club/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        hddolby: { name: '杜比', emoji: '🔊', url: 'https://www.hddolby.com/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        ssd: { name: '春天', emoji: '🌸', url: 'https://springsunday.net/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' },
        ubits: { name: '你堡', emoji: '🏯', url: 'https://ubits.club/torrents.php?search={imdb}&search_area=4', searchType: 'imdb' }
    };

    // --- 系统配置 ---
    const CONFIG = {
        cacheTTL: 10 * 60 * 1000,
        requestTimeout: 15000,
        maxRetries: 3,
        retryDelay: 1000,
        colors: {
            // 现代化淡雅色彩方案
            background: 'rgba(255, 255, 255, 0.95)',
            cardBg: 'rgba(248, 249, 252, 0.8)',
            border: 'rgba(226, 232, 240, 0.6)',
            success: 'rgba(16, 185, 129, 0.9)',        // 淡绿色
            successHover: 'rgba(5, 150, 105, 0.95)',
            warning: 'rgba(245, 158, 11, 0.9)',        // 淡橙色
            warningHover: 'rgba(217, 119, 6, 0.95)',
            error: 'rgba(239, 68, 68, 0.85)',          // 淡红色
            errorHover: 'rgba(220, 38, 38, 0.9)',
            loading: 'rgba(148, 163, 184, 0.8)',       // 淡灰色
            primary: 'rgba(59, 130, 246, 0.9)',        // 淡蓝色
            primaryHover: 'rgba(37, 99, 235, 0.95)',
            secondary: 'rgba(139, 92, 246, 0.85)',     // 淡紫色
            secondaryHover: 'rgba(124, 58, 237, 0.9)',
            text: 'rgba(30, 41, 59, 0.9)',
            textLight: 'rgba(71, 85, 105, 0.8)',
            shadow: 'rgba(0, 0, 0, 0.08)',
            shadowHover: 'rgba(0, 0, 0, 0.15)'
        },
        spacing: {
            xs: '4px',
            sm: '8px',
            md: '12px',
            lg: '16px',
            xl: '20px'
        },
        borderRadius: {
            sm: '6px',
            md: '10px',
            lg: '12px',
            xl: '16px'
        },
        fontSize: { 
            xs: '12px',
            sm: '13px', 
            md: '14px',
            lg: '15px'
        }
    };

    // ==================== 样式定义 ====================

    function injectStyles() {
        GM_addStyle(`
            /* 主容器 - 现代化卡片设计 */
            #emby-script-container {
                background: ${CONFIG.colors.background};
                backdrop-filter: blur(10px);
                border: 1px solid ${CONFIG.colors.border};
                border-radius: ${CONFIG.borderRadius.lg};
                padding: ${CONFIG.spacing.lg};
                margin: ${CONFIG.spacing.lg} 0;
                box-shadow: 0 4px 24px ${CONFIG.colors.shadow}, 0 2px 8px rgba(0, 0, 0, 0.04);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }

            #emby-script-container::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, 
                    ${CONFIG.colors.primary} 0%, 
                    ${CONFIG.colors.secondary} 50%, 
                    ${CONFIG.colors.success} 100%);
                opacity: 0.6;
            }

            /* 按钮栏布局 */
            #emby-script-button-bar {
                display: flex;
                flex-wrap: wrap;
                gap: ${CONFIG.spacing.sm};
                align-items: center;
                margin-bottom: ${CONFIG.spacing.xs};
            }

            /* 统一按钮样式 - 现代化设计 */
            .es-button {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                padding: ${CONFIG.spacing.sm} ${CONFIG.spacing.md};
                border-radius: ${CONFIG.borderRadius.sm};
                color: white !important;
                text-decoration: none !important;
                font-size: ${CONFIG.fontSize.sm};
                font-weight: 500;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
                transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
                border: none;
                cursor: pointer;
                gap: ${CONFIG.spacing.xs};
                white-space: nowrap;
                position: relative;
                overflow: hidden;
                letter-spacing: 0.025em;
                backdrop-filter: blur(8px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.05);
            }

            .es-button::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.6s ease;
            }

            .es-button:hover::before {
                left: 100%;
            }

            .es-button:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px ${CONFIG.colors.shadowHover}, 0 3px 8px rgba(0, 0, 0, 0.1);
                color: white !important;
                text-decoration: none !important;
            }

            .es-button:active {
                transform: translateY(0);
                transition: transform 0.1s ease;
            }

            /* 状态按钮样式 */
            .es-button.status-success { 
                background: linear-gradient(135deg, ${CONFIG.colors.success}, rgba(16, 185, 129, 0.8));
                border: 1px solid rgba(16, 185, 129, 0.3);
            }
            .es-button.status-success:hover { 
                background: linear-gradient(135deg, ${CONFIG.colors.successHover}, ${CONFIG.colors.success});
            }

            .es-button.status-warning { 
                background: linear-gradient(135deg, ${CONFIG.colors.warning}, rgba(245, 158, 11, 0.8));
                border: 1px solid rgba(245, 158, 11, 0.3);
            }
            .es-button.status-warning:hover { 
                background: linear-gradient(135deg, ${CONFIG.colors.warningHover}, ${CONFIG.colors.warning});
            }

            .es-button.status-error { 
                background: linear-gradient(135deg, ${CONFIG.colors.error}, rgba(239, 68, 68, 0.8));
                border: 1px solid rgba(239, 68, 68, 0.3);
                cursor: default;
            }
            .es-button.status-error:hover { 
                background: linear-gradient(135deg, ${CONFIG.colors.errorHover}, ${CONFIG.colors.error});
                transform: none;
            }

            .es-button.status-loading { 
                background: linear-gradient(135deg, ${CONFIG.colors.loading}, rgba(148, 163, 184, 0.7));
                border: 1px solid rgba(148, 163, 184, 0.3);
                cursor: default;
            }

            /* 外部链接按钮 */
            .es-button.external { 
                background: linear-gradient(135deg, ${CONFIG.colors.primary}, rgba(59, 130, 246, 0.8));
                border: 1px solid rgba(59, 130, 246, 0.3);
            }
            .es-button.external:hover { 
                background: linear-gradient(135deg, ${CONFIG.colors.primaryHover}, ${CONFIG.colors.primary});
            }

            /* PT站点按钮 */
            .es-button.pt-site { 
                background: linear-gradient(135deg, ${CONFIG.colors.secondary}, rgba(139, 92, 246, 0.8));
                border: 1px solid rgba(139, 92, 246, 0.3);
                min-width: 85px;
            }
            .es-button.pt-site:hover { 
                background: linear-gradient(135deg, ${CONFIG.colors.secondaryHover}, ${CONFIG.colors.secondary});
            }

            /* PT站点容器 - 优雅展开动画 */
            .pt-sites-container {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(85px, 1fr));
                gap: ${CONFIG.spacing.sm};
                max-height: 0;
                overflow: hidden;
                transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                opacity: 0;
                margin-top: 0;
                padding: 0 ${CONFIG.spacing.xs};
                border-radius: ${CONFIG.borderRadius.sm};
                background: ${CONFIG.colors.cardBg};
                backdrop-filter: blur(5px);
            }

            .pt-sites-container.expanded {
                max-height: 400px;
                opacity: 1;
                margin-top: ${CONFIG.spacing.md};
                padding: ${CONFIG.spacing.md} ${CONFIG.spacing.sm};
                border: 1px solid ${CONFIG.colors.border};
            }

            /* 切换图标动画 */
            .toggle-icon {
                transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                display: inline-block;
                font-size: ${CONFIG.fontSize.xs};
            }
            .toggle-icon.rotated {
                transform: rotate(180deg);
            }

            /* 现代化加载动画 */
            .loading-spinner {
                display: inline-block;
                width: 14px;
                height: 14px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                border-top: 2px solid rgba(255, 255, 255, 0.9);
                animation: modernSpin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
                margin-right: ${CONFIG.spacing.xs};
            }

            @keyframes modernSpin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                #emby-script-container {
                    margin: ${CONFIG.spacing.sm} 0;
                    padding: ${CONFIG.spacing.md};
                }
                
                #emby-script-button-bar {
                    gap: ${CONFIG.spacing.xs};
                }
                
                .es-button {
                    padding: ${CONFIG.spacing.xs} ${CONFIG.spacing.sm};
                    font-size: ${CONFIG.fontSize.xs};
                }
                
                .pt-sites-container {
                    grid-template-columns: repeat(auto-fill, minmax(75px, 1fr));
                }
            }

            /* 悬浮提示增强 */
            .es-button[title]:hover::after {
                content: attr(title);
                position: absolute;
                bottom: calc(100% + 8px);
                left: 50%;
                transform: translateX(-50%);
                background: rgba(30, 41, 59, 0.95);
                color: white;
                padding: ${CONFIG.spacing.xs} ${CONFIG.spacing.sm};
                border-radius: ${CONFIG.borderRadius.sm};
                font-size: ${CONFIG.fontSize.xs};
                white-space: nowrap;
                z-index: 1000;
                backdrop-filter: blur(8px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                pointer-events: none;
                opacity: 0;
                animation: tooltipFadeIn 0.3s ease-out 0.5s forwards;
            }

            @keyframes tooltipFadeIn {
                from { opacity: 0; transform: translateX(-50%) translateY(4px); }
                to { opacity: 1; transform: translateX(-50%) translateY(0); }
            }

            /* 成功状态特殊样式 */
            .es-button.status-success {
                background: linear-gradient(135deg, 
                    rgba(16, 185, 129, 0.9) 0%, 
                    rgba(5, 150, 105, 0.8) 50%,
                    rgba(16, 185, 129, 0.7) 100%);
                position: relative;
            }

            .es-button.status-success::after {
                content: '';
                position: absolute;
                top: 2px;
                left: 2px;
                right: 2px;
                bottom: 2px;
                background: linear-gradient(135deg, 
                    rgba(255, 255, 255, 0.1) 0%, 
                    transparent 50%);
                border-radius: calc(${CONFIG.borderRadius.sm} - 2px);
                pointer-events: none;
            }
        `);
    }

    // ==================== 工具函数 ====================

    function validateConfig() {
        const issues = [];
        if (!embyHost || embyHost.trim() === "" || embyHost.includes("填写")) {
            issues.push("Emby服务器地址未配置");
        }
        if (!embyApiKey || embyApiKey.trim() === "" || embyApiKey.includes("填写")) {
            issues.push("Emby API密钥未配置");
        }

        if (issues.length > 0) {
            console.error('[Emby Script] 配置验证失败:', issues);
            return { valid: false, issues };
        }

        console.log('[Emby Script] 配置验证通过');
        return { valid: true, issues: [] };
    }

    function requestEmby(url) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: "GET", url, timeout: CONFIG.requestTimeout,
                onload: res => (res.status >= 200 && res.status < 300) ? resolve(res.responseText) : reject(new Error(`HTTP ${res.status}: ${res.statusText}`)),
                onerror: () => reject(new Error('网络请求失败')),
                ontimeout: () => reject(new Error('请求超时'))
            });
        });
    }

    async function fetchWithRetry(url, maxRetries = CONFIG.maxRetries) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return JSON.parse(await requestEmby(url));
            } catch (error) {
                console.warn(`[Emby Script] 请求失败 (${attempt}/${maxRetries}):`, error.message);
                if (attempt === maxRetries) throw error;
                await new Promise(resolve => setTimeout(resolve, CONFIG.retryDelay * Math.pow(2, attempt - 1)));
            }
        }
    }

    // ==================== 数据提取与处理 ====================

    function extractDoubanInfo() {
        try {
            const douban_id = window.location.href.match(/subject\/(\d+)/)?.[1] || '';
            let unititle = document.querySelector("#content > h1 > span:nth-child(1)")?.textContent?.trim() || document.title.replace(/（.*/, "").replace(/\s*-\s*豆瓣.*/, "").trim();
            let imdb_id = "", year = "";
            const infoText = document.querySelector("#info")?.textContent || "";
            const imdbMatch = infoText.match(/IMDb:\s*(tt\d+)/i);
            if (imdbMatch) imdb_id = imdbMatch[1];
            const yearMatch = document.querySelector("#content .year")?.textContent.replace(/[()]/g, "").trim();
            if (yearMatch) year = yearMatch;

            const result = { imdb_id, unititle, douban_id, year };
            console.log('[Emby Script] 提取的豆瓣信息:', result);
            return result;
        } catch (error) {
            console.error('[Emby Script] 提取豆瓣信息失败:', error);
            return { imdb_id: "", unititle: "", douban_id: "", year: "" };
        }
    }

    // ==================== 缓存管理 ====================

    const CacheManager = {
        get(key) {
            try {
                const cached = JSON.parse(sessionStorage.getItem(`emby_script_${key}`));
                if (cached && Date.now() - cached.timestamp < CONFIG.cacheTTL) return cached.data;
                if (cached) this.remove(key);
            } catch (e) { this.remove(key); }
            return null;
        },
        set(key, data) {
            try { sessionStorage.setItem(`emby_script_${key}`, JSON.stringify({ timestamp: Date.now(), data })); } catch (e) { console.warn(`[Emby Script] 缓存保存失败: ${key}`, e); }
        },
        remove(key) { sessionStorage.removeItem(`emby_script_${key}`); }
    };

    // ==================== UI 生成与渲染 ====================

    function generateSiteLinks(movieInfo) {
        let ptLinksHTML = '', externalLinksHTML = '', ptToggleButtonHTML = '';

        if (movieInfo.unititle) {
            externalLinksHTML += `<a href="https://www.themoviedb.org/search?query=${encodeURIComponent(movieInfo.unititle)}" target="_blank" class="es-button external" title="在TMDB中搜索该影片">🎬 TMDB</a>`;
        }
        if (embyHost && embyApiKey && movieInfo.unititle) {
            // 优化：使用Emby Web界面的搜索页面，避免API链接问题
            const embySearchUrl = `${embyHost}/web/index.html#!/search.html?query=${encodeURIComponent(movieInfo.unititle)}`;
            externalLinksHTML += `<a href="${embySearchUrl}" target="_blank" class="es-button external" title="在Emby Web界面中搜索该影片">🔍 Emby搜索</a>`;
        }

        const ptLinks = Object.values(SITES_CONFIG).map(site => {
            let url = site.url;
            if (site.searchType === 'imdb' && movieInfo.imdb_id) url = url.replace('{imdb}', movieInfo.imdb_id);
            else if (site.searchType === 'title' && movieInfo.unititle) url = url.replace('{title}', encodeURIComponent(movieInfo.unititle));
            else return null;
            return `<a href="${url}" target="_blank" class="es-button pt-site" title="在${site.name}中搜索资源">${site.emoji} ${site.name}</a>`;
        }).filter(Boolean);

        if (ptLinks.length > 0) {
            ptLinksHTML = ptLinks.join('');
            ptToggleButtonHTML = `<button id="pt-toggle-btn" class="es-button external" title="展开/收起PT站点列表"><span class="toggle-icon" id="toggle-icon">▼</span> PT站点 (${ptLinks.length}个)</button>`;
        }
        return { externalLinksHTML, ptToggleButtonHTML, ptLinksHTML };
    }

    function insertUI(embyStatusHTML, linkComponents) {
        try {
            const oldContainer = document.getElementById('emby-script-container');
            if (oldContainer) oldContainer.remove();

            const { externalLinksHTML, ptToggleButtonHTML, ptLinksHTML } = linkComponents;
            const containerHTML = `
                <div id="emby-script-container">
                    <div id="emby-script-button-bar">
                        ${embyStatusHTML}
                        ${externalLinksHTML}
                        ${ptToggleButtonHTML}
                    </div>
                    <div class="pt-sites-container" id="pt-sites-container">${ptLinksHTML}</div>
                </div>`;

            const h1Element = document.querySelector("#content h1");
            if (!h1Element) { console.error('[Emby Script] 无法找到页面H1标题元素'); return; }
            h1Element.insertAdjacentHTML('afterend', containerHTML);
            h1Element.style.paddingBottom = '8px';

            const toggleBtn = document.getElementById('pt-toggle-btn');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', () => {
                    document.getElementById('pt-sites-container')?.classList.toggle('expanded');
                    document.getElementById('toggle-icon')?.classList.toggle('rotated');
                });
            }
            console.log('[Emby Script] UI 插入/更新完成');
        } catch (error) { console.error('[Emby Script] UI 插入失败:', error); }
    }

    function showLoadingStatus(movieInfo) {
        const loadingHTML = `<div class="es-button status-loading"><span class="loading-spinner"></span>正在检查媒体库...</div>`;
        const linkComponents = generateSiteLinks(movieInfo);
        insertUI(loadingHTML, linkComponents);
    }

    function updateStatus(html) {
        const buttonBar = document.getElementById('emby-script-button-bar');
        if (buttonBar && buttonBar.firstChild) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;
            if (tempDiv.firstChild) {
                buttonBar.replaceChild(tempDiv.firstChild, buttonBar.firstChild);
            }
        }
    }

    // ==================== Emby 媒体检查 ====================

    /**
     * 通过IMDb ID进行精确匹配
     * 这是最准确的匹配方式，因为IMDb ID是全球唯一的
     */
    async function searchEmbyByImdb(imdb_id) {
        const url = `${embyHost}/emby/Items?Recursive=True&AnyProviderIdEquals=imdb.${imdb_id}&api_key=${embyApiKey}`;
        const response = await fetchWithRetry(url);
        if (response.TotalRecordCount > 0 && response.Items?.[0]) {
            const item = response.Items[0];
            const jumpUrl = `${embyHost}/web/index.html#!/item?id=${item.Id}&serverId=${item.ServerId}`;
            return {
                html: `<a href="${jumpUrl}" target="_blank" class="es-button status-success" title="通过IMDb ID精确匹配，点击跳转到Emby观看">✨ 媒体已收藏</a>`,
                status: 'found'
            };
        }
        return { status: 'not_found' };
    }

    /**
     * 通过标题进行模糊匹配
     * 当IMDb ID匹配失败时使用，通常是因为媒体库中的元数据缺少IMDb ID
     * 优化：直接跳转到第一个匹配结果的详情页，避免搜索链接问题
     */
    async function searchEmbyByTitle(title) {
        const url = `${embyHost}/emby/Items?IncludeItemTypes=Movie,Series&Recursive=True&api_key=${embyApiKey}&SearchTerm=${encodeURIComponent(title)}`;
        const response = await fetchWithRetry(url);
        if (response.TotalRecordCount > 0 && response.Items?.[0]) {
            // 关键优化：直接跳转到第一个匹配结果的详情页，而不是搜索页面
            // 这样可以绕过反向代理可能导致的搜索参数丢失问题
            const firstItem = response.Items[0];
            const jumpUrl = `${embyHost}/web/index.html#!/item?id=${firstItem.Id}&serverId=${firstItem.ServerId}`;
            return {
                html: `<a href="${jumpUrl}" target="_blank" class="es-button status-warning" title="通过标题匹配找到疑似影片：${firstItem.Name}，点击查看详情页面">? 疑似存在</a>`,
                status: 'maybe_found',
                item: firstItem
            };
        }
        return {
            html: `<div class="es-button status-error" title="媒体库中暂无此影片">📭 暂未收藏</div>`,
            status: 'not_found'
        };
    }

    async function checkEmby(movieInfo) {
        if (!embyHost || !embyApiKey) {
            console.warn('[Emby Script] Emby配置未完成 - 请检查embyHost和embyApiKey设置');
            return {
                html: `<div class="es-button status-error" title="请先配置Emby服务器地址和API密钥">⚙️ 配置未完成</div>`,
                status: 'config_error'
            };
        }

        const { imdb_id, unititle } = movieInfo;
        const cacheKey = `emby_result_${imdb_id || unititle}`;
        const cachedResult = CacheManager.get(cacheKey);
        if (cachedResult) {
            console.log('[Emby Script] 使用缓存结果:', cacheKey);
            return cachedResult;
        }

        try {
            // 优先使用IMDb ID进行精确匹配
            if (imdb_id) {
                console.log('[Emby Script] 尝试IMDb ID精确匹配:', imdb_id);
                const result = await searchEmbyByImdb(imdb_id);
                if (result.status === 'found') {
                    console.log('[Emby Script] IMDb ID匹配成功');
                    CacheManager.set(cacheKey, result);
                    return result;
                }
                console.log('[Emby Script] IMDb ID未找到匹配，尝试标题模糊匹配');
            }

            // 使用标题进行模糊匹配
            if (unititle) {
                console.log('[Emby Script] 尝试标题模糊匹配:', unititle);
                const result = await searchEmbyByTitle(unititle);
                console.log('[Emby Script] 标题匹配结果:', result.status);
                CacheManager.set(cacheKey, result);
                return result;
            }

            console.warn('[Emby Script] 缺少有效的搜索条件 (IMDb ID 或标题)');
            return {
                html: `<div class="es-button status-error" title="无法获取有效的影片信息">❓ 信息不足</div>`,
                status: 'no_data'
            };
        } catch (error) {
            console.error('[Emby Script] Emby查询失败:', error);
            return {
                html: `<div class="es-button status-error" title="网络连接或服务器出现问题: ${error.message}">❌ 连接失败</div>`,
                status: 'error'
            };
        }
    }

    // ==================== 主程序入口 ====================

    async function main() {
        if (!window.location.href.includes('movie.douban.com/subject/')) return;
        await new Promise(resolve => window.addEventListener('load', resolve, { once: true }));
        setTimeout(async () => {
            try {
                console.log('[Emby Script] 开始初始化...');

                // 验证配置
                const configValidation = validateConfig();
                if (!configValidation.valid) {
                    console.error('[Emby Script] 配置验证失败，请检查脚本配置');
                }

                injectStyles();
                const movieInfo = extractDoubanInfo();
                if (!movieInfo.unititle && !movieInfo.imdb_id) {
                    console.warn('[Emby Script] 无法提取有效的电影信息');
                    return;
                }
                showLoadingStatus(movieInfo);
                const embyResult = await checkEmby(movieInfo);
                updateStatus(embyResult.html);
                console.log('[Emby Script] 初始化完成');
            } catch (error) {
                console.error('[Emby Script] 初始化失败:', error);
                updateStatus(`<div class="es-button status-error" title="初始化失败: ${error.message}">❌ 初始化失败</div>`);
            }
        }, 1000);
    }

    main();
})();